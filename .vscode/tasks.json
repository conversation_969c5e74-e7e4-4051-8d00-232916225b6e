// Available variables which can be used inside of strings.
// ${workspaceRoot}: the root folder of the team
// ${file}: the current opened file
// ${fileBasename}: the current opened file's basename
// ${fileDirname}: the current opened file's dirname
// ${fileExtname}: the current opened file's extension
// ${cwd}: the current working directory of the spawned process
// A task runner that calls a custom npm script that compiles the extension.
{
	"version": "2.0.0",
	"presentation": {
		"echo": false,
		"reveal": "always",
		"focus": false,
		"panel": "dedicated",
		"showReuseMessage": false
	},
	"tasks": [
		{
			"type": "npm",
			"script": "build",
			"group": "build",
			"problemMatcher": ["$ts-checker-webpack", "$ts-checker-eslint-webpack"]
		},
		{
			"type": "npm",
			"script": "lint",
			"group": "build",
			"problemMatcher": ["$eslint-stylish"]
		},
		{
			"type": "npm",
			"script": "watch",
			"group": {
				"kind": "build",
				"isDefault": true
			},
			"isBackground": true,
			"presentation": {
				"group": "watchers"
			},
			"problemMatcher": ["$ts-checker-webpack-watch", "$ts-checker-eslint-webpack-watch"]
		},
		{
			"type": "npm",
			"script": "watch:quick",
			"group": {
				"kind": "build"
			},
			"isBackground": true,
			"presentation": {
				"group": "watchers"
			},
			"problemMatcher": ["$ts-checker-webpack-watch"]
		},
		{
			"label": "Install dependencies",
			"type": "shell",
			"command": "pnpm",
			"args": ["install"],
			"presentation": {
				"close": true,
				"reveal": "never"
			},
			"runOptions": {
				"runOn": "folderOpen"
			}
		},
		{
			"label": "Run (local web)",
			"type": "npm",
			"script": "web",
			"group": "test",
			"isBackground": true,
			"presentation": {
				"group": "web",
				"reveal": "never"
			},
			"problemMatcher": []
		},
		{
			"label": "Run (vscode.dev)",
			"type": "npm",
			"script": "web:serve",
			"group": "test",
			"isBackground": true,
			"presentation": {
				"group": "web",
				"reveal": "never"
			},
			"problemMatcher": []
		},
		{
			"type": "npm",
			"script": "build:tests",
			"group": {
				"kind": "build"
			},
			"presentation": {
				"group": "build"
			}
		},
		{
			"type": "npm",
			"script": "watch:tests",
			"group": "build",
			"isBackground": true,
			"presentation": {
				"group": "watchers"
			},
			"problemMatcher": "$tsc-watch" //["$ts-checker-webpack-watch", "$ts-checker-eslint-webpack-watch"]
		}
	]
}
