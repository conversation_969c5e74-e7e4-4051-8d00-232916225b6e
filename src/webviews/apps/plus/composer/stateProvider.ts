import { ContextProvider, createContext } from '@lit/context';
import { debounce } from '../../../../system/function/debounce';
import type { ComposerCommit, ComposerHunk, ComposerHunkMap, State } from '../../../plus/composer/protocol';
import {
	DidChangeAiEnabledNotification,
	DidFinishCommittingNotification,
	DidGenerateCommitMessageNotification,
	DidGenerateCommitsNotification,
	DidStartCommittingNotification,
	DidStartGeneratingCommitMessageNotification,
	DidStartGeneratingNotification,
} from '../../../plus/composer/protocol';
import { updateHunkAssignments } from '../../../plus/composer/utils';
import type { ReactiveElementHost, StateProvider } from '../../shared/appHost';
import { signalObjectState, signalState } from '../../shared/components/signal-utils';
import type { Disposable } from '../../shared/events';
import type { HostIpc } from '../../shared/ipc';
import { stateContext } from './context';

// Internal history management interfaces
export interface ComposerDataSnapshot {
	hunks: ComposerHunk[];
	commits: ComposerCommit[];
	selectedCommitId: string | null;
	selectedCommitIds: Set<string>;
	selectedUnassignedSection: 'staged' | 'unstaged' | 'unassigned' | null;
	selectedHunkIds: Set<string>;
}

export interface ComposerHistory {
	resetState: ComposerDataSnapshot | null;
	undoStack: ComposerDataSnapshot[];
	redoStack: ComposerDataSnapshot[];
}

export const historyLimit = 3 as const;

export interface AppState {
	// host
	hunks: ComposerHunk[];
	commits: ComposerCommit[];
	hunkMap: ComposerHunkMap[];
	baseCommit: string;
	generatingCommitMessage: string | null; // commitId of the commit currently generating a message, or null

	// local state
	selectedCommitId: string | null;
	selectedCommitIds: Set<string>;
	selectedUnassignedSection: 'staged' | 'unstaged' | 'unassigned' | null;
	selectedHunkId: string | null;
	selectedHunkIds: Set<string>;
	hunksWithAssignments: ComposerHunk[];
	isAiEnabled: boolean;
	canFinishAndCommit: boolean;
	detailsSectionExpanded: {
		commitMessage: boolean;
		aiExplanation: boolean;
		filesChanged: boolean;
	};
	generatingCommits: boolean;
	committing: boolean; // true if currently committing, false if not
}

export const composerStateContext = createContext<ComposerStateProvider>('composer-state');

export class ComposerStateProvider implements StateProvider<State>, AppState {
	private readonly disposable: Disposable;
	private readonly provider: ContextProvider<{ __context__: ComposerStateProvider }, ReactiveElementHost>;
	// private readonly provider: ContextProvider<{ __context__: State }, ReactiveElementHost>;

	private readonly _state: State;
	get state(): State {
		return this._state;
	}

	@signalState()
	accessor hunks: ComposerHunk[] = [];

	@signalState()
	accessor commits: ComposerCommit[] = [];

	@signalState()
	accessor hunkMap: ComposerHunkMap[] = [];

	@signalState()
	accessor baseCommit: string = '';

	@signalState()
	accessor selectedCommitId: string | null = null;

	@signalState()
	accessor selectedUnassignedSection: 'staged' | 'unstaged' | 'unassigned' | null = null;

	@signalState()
	accessor selectedCommitIds: Set<string> = new Set();

	@signalState()
	accessor selectedHunkId: string | null = null;

	@signalState()
	accessor selectedHunkIds: Set<string> = new Set();

	@signalState()
	accessor lastSelectedHunkId: string | null = null;

	@signalObjectState()
	accessor detailsSectionExpanded = {
		commitMessage: true,
		aiExplanation: true,
		filesChanged: true,
	};

	@signalState()
	accessor history: ComposerHistory = {
		resetState: null,
		undoStack: [],
		redoStack: [],
	};

	@signalState()
	accessor generatingCommits = false;

	get generatingCommitMessage(): string | null {
		return this._state.generatingCommitMessage;
	}

	@signalState()
	accessor committing = false;

	get hunksWithAssignments(): ComposerHunk[] {
		if (!this._state?.hunks || !this._state?.commits) {
			return [];
		}

		return updateHunkAssignments(this._state.hunks, this._state.commits);
	}

	get isAiEnabled(): boolean {
		return this._state?.aiEnabled?.org === true && this._state?.aiEnabled?.config === true;
	}

	get canFinishAndCommit(): boolean {
		return this._state.commits.length > 0;
	}

	constructor(
		host: ReactiveElementHost,
		state: State,
		private readonly _ipc: HostIpc,
	) {
		this._state = state;
		this.provider = new ContextProvider(host, { context: composerStateContext, initialValue: this });
		// this.provider = new ContextProvider(host, { context: stateContext, initialValue: this._state });

		this.disposable = this._ipc.onReceiveMessage(msg => {
			switch (true) {
				case DidStartGeneratingNotification.is(msg): {
					const updatedState = {
						generatingCommits: true,
						timestamp: Date.now(),
					};

					this.updateState(updatedState);
					break;
				}
				case DidStartGeneratingCommitMessageNotification.is(msg): {
					const updatedState = {
						generatingCommitMessage: msg.params.commitId,
						timestamp: Date.now(),
					};

					this.updateState(updatedState);
					break;
				}
				case DidGenerateCommitsNotification.is(msg): {
					const updatedState = {
						generatingCommits: false,
						commits: msg.params.commits,
						hunks: this._state.hunks.map(hunk => ({
							...hunk,
							assigned: true,
						})),
						timestamp: Date.now(),
					};

					this.updateState(updatedState);
					break;
				}
				case DidGenerateCommitMessageNotification.is(msg): {
					const updatedCommits = this._state.commits.map(commit =>
						commit.id === msg.params.commitId ? { ...commit, message: msg.params.message } : commit,
					);

					const updatedState = {
						generatingCommitMessage: null,
						commits: updatedCommits,
						timestamp: Date.now(),
					};

					this.updateState(updatedState);
					break;
				}
				case DidStartCommittingNotification.is(msg): {
					const updatedState = {
						committing: true,
						timestamp: Date.now(),
					};

					this.updateState(updatedState);
					break;
				}
				case DidFinishCommittingNotification.is(msg): {
					const updatedState = {
						committing: false,
						timestamp: Date.now(),
					};

					this.updateState(updatedState);
					break;
				}
				case DidChangeAiEnabledNotification.is(msg): {
					const updatedState = {
						aiEnabled: {
							...this._state.aiEnabled,
							...(msg.params.org !== undefined && { org: msg.params.org }),
							...(msg.params.config !== undefined && { config: msg.params.config }),
						},
						timestamp: Date.now(),
					};

					this.updateState(updatedState);
					break;
				}
			}
		});
	}

	private updateState(partial: Partial<State>, silent?: boolean) {
		for (const key in partial) {
			const value = partial[key as keyof State];
			// @ts-expect-error key is a key of State
			this._state[key] = value;

			if (key in this) {
				// @ts-expect-error key is a key of State
				this[key] = value;
			}
		}

		if (silent) return;

		this.fireProviderUpdate();
	}

	private fireProviderUpdate = debounce(() => this.provider.setValue(this, true), 100);
	// private fireProviderUpdate = debounce(() => this.provider.setValue(this._state, true), 100);

	// updateSelectedCommit(commitId: string | null, multiSelect: boolean = false) {
	// 	if (multiSelect && commitId) {
	// 		const newSelection = new Set(this._state.selectedCommitIds);
	// 		if (newSelection.has(commitId)) {
	// 			newSelection.delete(commitId);
	// 		} else {
	// 			newSelection.add(commitId);
	// 		}
	// 		this._state.selectedCommitIds = newSelection;

	// 		if (newSelection.size > 1) {
	// 			this._state.selectedCommitId = null;
	// 		} else if (newSelection.size === 1) {
	// 			this._state.selectedCommitId = Array.from(newSelection)[0];
	// 			this._state.selectedCommitIds = new Set();
	// 		} else {
	// 			this._state.selectedCommitId = null;
	// 		}
	// 	} else {
	// 		this._state.selectedCommitIds = new Set();
	// 		this._state.selectedCommitId = commitId;
	// 	}

	// 	this._state.selectedUnassignedSection = null;
	// 	this._state.timestamp = Date.now();
	// 	this.fireProviderUpdate();
	// }

	// updateSelectedUnassignedSection(section: string | null) {
	// 	this._state.selectedUnassignedSection = section;
	// 	this._state.selectedCommitId = null;
	// 	this._state.selectedCommitIds = new Set();
	// 	this._state.timestamp = Date.now();
	// 	this.fireProviderUpdate();
	// }

	// updateSelectedHunks(hunkId: string, multiSelect: boolean = false) {
	// 	if (multiSelect) {
	// 		const newSelection = new Set(this._state.selectedHunkIds);
	// 		if (newSelection.has(hunkId)) {
	// 			newSelection.delete(hunkId);
	// 		} else {
	// 			newSelection.add(hunkId);
	// 		}
	// 		this._state.selectedHunkIds = newSelection;
	// 	} else {
	// 		this._state.selectedHunkIds = new Set([hunkId]);
	// 	}

	// 	this._state.timestamp = Date.now();
	// 	this.fireProviderUpdate();
	// }

	// updateSectionExpansion(section: 'commitMessage' | 'aiExplanation' | 'filesChanged', expanded: boolean) {
	// 	switch (section) {
	// 		case 'commitMessage':
	// 			this._state.detailsSectionExpanded.commitMessage = expanded;
	// 			break;
	// 		case 'aiExplanation':
	// 			this._state.detailsSectionExpanded.aiExplanation = expanded;
	// 			break;
	// 		case 'filesChanged':
	// 			this._state.detailsSectionExpanded.filesChanged = expanded;
	// 			break;
	// 	}

	// 	this._state.timestamp = Date.now();
	// 	this.fireProviderUpdate();
	// }

	dispose(): void {
		this.disposable.dispose();
	}
}
