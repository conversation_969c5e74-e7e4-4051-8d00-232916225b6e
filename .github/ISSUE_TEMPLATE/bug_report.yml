name: Bug report
description: Create a report to help GitLens improve
type: 'Bug'
labels: ['triage']
body:
    - type: textarea
      attributes:
          label: Description
          description: Please provide a concise description of what you're experiencing, what you expected to happen, and any steps to reproduce the behavior.
          placeholder: |
              1. In this environment...
              2. With this config...
              3. Run '...'
              4. See error...
      validations:
          required: true
    - type: input
      id: gitlens
      attributes:
          label: GitLens Version
          description: What version of GitLens are you using?
          placeholder: 16.0.0
      validations:
          required: true
    - type: textarea
      id: vscode
      attributes:
          label: VS Code Version
          description: What version of VS Code are you using? Copy from Help -> About
          placeholder: |
              Version: 1.98.0-insider
              Commit: 44028decf1ca81a0cbf99a65ab72cca6735449f2
              Date: 2025-02-14T05:04:46.748Z
              Browser: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Code-Insiders/1.98.0-insider Chrome/132.0.6834.194 Electron/34.1.0 Safari/537.36
      validations:
          required: false
    - type: input
      id: git
      attributes:
          label: Git Version
          description: 'What version of Git are you using? Use `git --version`'
          placeholder: 'git version 2.47.1.windows.2'
      validations:
          required: false
    - type: textarea
      attributes:
          label: Logs, Screenshots, Screen Captures, etc
          description: |
              Logs? Links? References? Anything that will give us more context about the issue you are encountering!
          placeholder: |
              Please enable debug logging by running the _GitLens: Enable Debug Logging_ command from the Command Palette (<kbd>F1</kbd> or <kbd>ctrl/cmd</kbd>+<kbd>shift</kbd>+<kbd>p</kbd>).
              This will enable logging to the _GitLens_ & _GitLens (Git)_ channels in the _Output_ pane.

              Once enabled, please reproduce the issue and use the _Export Logs..._ command in the Output pane's ..` menu and select both the _GitLens_ & _GitLens (Git)_.
      validations:
          required: false
