name: Publish pre-release

on:
    schedule:
        - cron: '0 9 * * *' # every day at 4am EST
    workflow_dispatch:

jobs:
    check:
        name: Check for updates
        runs-on: ubuntu-latest
        permissions:
            contents: write
        outputs:
            status: ${{ steps.earlyexit.outputs.status }}
        steps:
            - name: Checkout code
              uses: actions/checkout@v4
              with:
                  fetch-depth: 0
            - id: earlyexit
              run: |
                  git config user.name github-actions
                  git config user.email <EMAIL>
                  if git rev-parse origin/pre >/dev/null 2>&1; then
                    preRef=$(git show-ref -s origin/pre)
                    headRef=$(git show-ref --head -s head)
                    echo "origin/pre"
                    echo $preRef
                    echo "HEAD"
                    echo $headRef
                    if [ "$preRef" = "$headRef" ]; then
                      echo "No changes since last pre-release build. Exiting."
                      echo "status=unchanged" >> $GITHUB_OUTPUT
                      exit 0
                    else
                      echo "Updating pre"
                      git push origin --delete pre
                      git checkout -b pre
                      git push origin pre
                    fi
                  else
                    echo "No pre branch. Creating."
                    git checkout -b pre
                    git push origin pre
                  fi
                  echo "status=changed" >> $GITHUB_OUTPUT

    publish:
        name: Publish pre-release
        needs: check
        runs-on: ubuntu-latest
        if: needs.check.outputs.status == 'changed'
        steps:
            - name: Checkout code
              uses: actions/checkout@v4
            - name: Setup node
              uses: actions/setup-node@v4
              with:
                  node-version: '22'
            - name: Setup pnpm
              uses: pnpm/action-setup@v3
              with:
                  version: 10
            - name: Install
              run: pnpm install
            - name: Apply pre-release patch
              run: pnpm run patch-pre
            - name: Setup Environment
              run: node -e "console.log('PACKAGE_VERSION=' + require('./package.json').version + '\nPACKAGE_NAME=' + require('./package.json').name + '-' + require('./package.json').version)" >> $GITHUB_ENV
            - name: Package extension
              run: pnpm run package --pre-release
            - name: Publish extension to VS Code Marketplace
              run: pnpm vsce publish --no-dependencies --pre-release --packagePath ./${{ env.PACKAGE_NAME }}.vsix -p ${{ secrets.GITLENS_VSCODE_MARKETPLACE_PAT }}
            - name: Publish extension to OpenVSIX
              run: pnpm ovsx publish ./${{ env.PACKAGE_NAME }}.vsix -p ${{ secrets.GITLENS_OPENVSIX_PAT }}
            - name: Publish artifact
              uses: actions/upload-artifact@v4
              with:
                  name: ${{ env.PACKAGE_NAME }}.vsix
                  path: ./${{ env.PACKAGE_NAME }}.vsix
