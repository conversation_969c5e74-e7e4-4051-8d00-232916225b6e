name: 'Lock closed issues'

on:
    schedule:
        - cron: '30 5 * * *'
    workflow_dispatch:

jobs:
    stale:
        runs-on: ubuntu-latest
        permissions:
            issues: write
        steps:
            - uses: dessant/lock-threads@v5
              with:
                  github-token: ${{ secrets.GITHUB_TOKEN }}
                  issue-comment: 'This issue has been automatically locked since there has not been any recent activity after it was closed. Please open a new issue for related bugs.'
                  issue-inactive-days: 30
                  process-only: 'issues'
